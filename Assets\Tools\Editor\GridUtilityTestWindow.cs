using UnityEditor;
using UnityEngine;
using SmartVertex.Tools;

namespace SmartVertex.EditorTools
{
    /// <summary>
    /// Editor window for testing GridUtility functionality in both 2D and 3D spaces.
    /// </summary>
    public class GridUtilityTestWindow : BaseDebugWindow
    {
        #region Fields

        private GameObject testGridContainer;
        private Vector2Int gridSize = new Vector2Int(5, 5);
        private Vector2Int selectedCell = new Vector2Int(0, 0);
        private Vector2 cellOffset = new Vector2(0.5f, 0.5f);
        private Vector3 gridOffset = Vector3.zero;
        private GridUtility.GridPlane selectedPlane = GridUtility.GridPlane.XZ;
        private Bounds customBounds = new Bounds(Vector3.zero, Vector3.one * 10f);
        private bool useGameObject = true;
        private bool showGridVisualization = true;
        private bool showCellHighlight = true;
        private Color gridColor = Color.white;
        private Color cellHighlightColor = Color.red;
        private bool foldoutSettings = true;
        private bool foldoutResults = true;
        private bool foldoutSnapping = true;
        private bool foldoutBounds = true;
        private bool foldoutMultiCell = true;
        private bool foldoutValidation = true;
        private bool foldoutDistance = true;
        private bool foldoutVisualization = true;

        // Test results
        private Vector3 calculatedPosition;
        private Vector2Int calculatedCoords;
        private bool hasValidBounds;
        private string lastError = "";

        // Snap testing
        private Vector3 snapTestPosition = Vector3.zero;
        private Vector3 snappedPosition;

        // Multi-cell testing
        private Bounds testArea = new Bounds(Vector3.zero, Vector3.one * 5f);
        private Vector2Int[] testCellArray = new Vector2Int[] { new Vector2Int(0, 0), new Vector2Int(1, 1), new Vector2Int(2, 2) };

        // Distance testing
        private Vector2Int cellA = new Vector2Int(0, 0);
        private Vector2Int cellB = new Vector2Int(3, 4);
        private int radiusTest = 2;

        #endregion

        #region Unity Editor Methods

        /// <summary>
        /// Shows the GridUtility Test window.
        /// </summary>
        public static void ShowWindow()
        {
            var window = GetWindow<GridUtilityTestWindow>("Grid Utility Tester");
            window.minSize = new Vector2(400, 600);
            window.Show();
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            SceneView.duringSceneGui += OnSceneGUI;
        }

        private void OnDisable()
        {
            SceneView.duringSceneGui -= OnSceneGUI;
        }

        private void OnGUI()
        {
            InitializeStyles();
            DrawHeader("Grid Utility Tester", "Refresh calculations", "Clear test data");

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            DrawSettingsSection();
            DrawResultsSection();
            DrawSnappingSection();
            DrawBoundsSection();
            DrawMultiCellSection();
            DrawValidationSection();
            DrawDistanceSection();
            DrawVisualizationSection();
            DrawTestButtons();

            EditorGUILayout.EndScrollView();
        }

        #endregion

        #region GUI Drawing Methods

        private void DrawSettingsSection()
        {
            foldoutSettings = EditorGUILayout.Foldout(foldoutSettings, "Grid Settings", foldoutStyle);
            if (!foldoutSettings) return;

            EditorGUILayout.BeginVertical(boxStyle);

            // Grid container selection
            useGameObject = EditorGUILayout.Toggle("Use GameObject", useGameObject);

            if (useGameObject)
            {
                EditorGUI.BeginChangeCheck();
                testGridContainer = (GameObject)EditorGUILayout.ObjectField(
                    "Grid Container", testGridContainer, typeof(GameObject), true);
                if (EditorGUI.EndChangeCheck())
                {
                    CalculateResults();
                }
            }
            else
            {
                EditorGUILayout.LabelField("Custom Bounds", subHeaderStyle);
                EditorGUI.BeginChangeCheck();
                Vector3 center = EditorGUILayout.Vector3Field("Center", customBounds.center);
                Vector3 size = EditorGUILayout.Vector3Field("Size", customBounds.size);
                if (EditorGUI.EndChangeCheck())
                {
                    customBounds = new Bounds(center, size);
                    CalculateResults();
                }
            }

            // Grid configuration
            EditorGUI.BeginChangeCheck();
            gridSize = EditorGUILayout.Vector2IntField("Grid Size", gridSize);
            selectedCell = EditorGUILayout.Vector2IntField("Selected Cell", selectedCell);
            cellOffset = EditorGUILayout.Vector2Field("Cell Offset", cellOffset);
            gridOffset = EditorGUILayout.Vector3Field("Grid Offset", gridOffset);
            selectedPlane = (GridUtility.GridPlane)EditorGUILayout.EnumPopup("Grid Plane", selectedPlane);
            if (EditorGUI.EndChangeCheck())
            {
                CalculateResults();
            }

            // Indexing mode
            bool oneBasedIndexing = EditorGUILayout.Toggle("One-Based Indexing", GridUtility.UseOneBasedIndexing);
            if (oneBasedIndexing != GridUtility.UseOneBasedIndexing)
            {
                GridUtility.UseOneBasedIndexing = oneBasedIndexing;
                CalculateResults();
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawResultsSection()
        {
            foldoutResults = EditorGUILayout.Foldout(foldoutResults, "Test Results", foldoutStyle);
            if (!foldoutResults) return;

            EditorGUILayout.BeginVertical(boxStyle);

            if (!string.IsNullOrEmpty(lastError))
            {
                EditorGUILayout.HelpBox(lastError, MessageType.Error);
            }

            EditorGUILayout.LabelField("Has Valid Bounds", hasValidBounds.ToString());
            EditorGUILayout.Vector3Field("Calculated Position", calculatedPosition);
            EditorGUILayout.Vector2IntField("Calculated Coordinates", calculatedCoords);

            // Show bounds info
            if (useGameObject && testGridContainer != null)
            {
                if (GridUtility.TryGetBounds(testGridContainer, out var bounds))
                {
                    EditorGUILayout.LabelField("GameObject Bounds:", subHeaderStyle);
                    EditorGUILayout.Vector3Field("Center", bounds.center);
                    EditorGUILayout.Vector3Field("Size", bounds.size);
                }
            }
            else if (!useGameObject)
            {
                EditorGUILayout.LabelField("Custom Bounds:", subHeaderStyle);
                EditorGUILayout.Vector3Field("Center", customBounds.center);
                EditorGUILayout.Vector3Field("Size", customBounds.size);
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawSnappingSection()
        {
            foldoutSnapping = EditorGUILayout.Foldout(foldoutSnapping, "Grid Snapping", foldoutStyle);
            if (!foldoutSnapping) return;

            EditorGUILayout.BeginVertical(boxStyle);

            EditorGUILayout.LabelField("Snap Testing", subHeaderStyle);

            EditorGUI.BeginChangeCheck();
            snapTestPosition = EditorGUILayout.Vector3Field("Test Position", snapTestPosition);
            if (EditorGUI.EndChangeCheck())
            {
                CalculateSnapResults();
            }

            EditorGUILayout.Vector3Field("Snapped Position", snappedPosition);

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Snap to Center"))
            {
                CalculateSnapResults(new Vector2(0.5f, 0.5f));
            }
            if (GUILayout.Button("Snap to Corner"))
            {
                CalculateSnapResults(Vector2.zero);
            }
            if (GUILayout.Button("Snap to Current Offset"))
            {
                CalculateSnapResults();
            }
            EditorGUILayout.EndHorizontal();

            if (GUILayout.Button("Set Test Position to Scene View Center"))
            {
                if (SceneView.lastActiveSceneView != null)
                {
                    snapTestPosition = SceneView.lastActiveSceneView.camera.transform.position;
                    CalculateSnapResults();
                }
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawBoundsSection()
        {
            foldoutBounds = EditorGUILayout.Foldout(foldoutBounds, "Grid Bounds", foldoutStyle);
            if (!foldoutBounds) return;

            EditorGUILayout.BeginVertical(boxStyle);

            EditorGUILayout.LabelField("Cell Bounds Testing", subHeaderStyle);

            if (hasValidBounds)
            {
                Bounds cellBounds;
                if (useGameObject && testGridContainer != null)
                {
                    cellBounds = GridUtility.GetCellBounds(testGridContainer, gridSize, selectedCell, selectedPlane, gridOffset);
                }
                else
                {
                    cellBounds = GridUtility.GetCellBounds(customBounds, gridSize, selectedCell, selectedPlane, gridOffset);
                }

                EditorGUILayout.Vector3Field("Cell Center", cellBounds.center);
                EditorGUILayout.Vector3Field("Cell Size", cellBounds.size);

                // Grid bounds with offset
                Bounds gridBounds;
                if (useGameObject && testGridContainer != null)
                {
                    GridUtility.TryGetBounds(testGridContainer, out var originalBounds);
                    gridBounds = GridUtility.GetGridBounds(originalBounds, gridOffset);
                }
                else
                {
                    gridBounds = GridUtility.GetGridBounds(customBounds, gridOffset);
                }

                EditorGUILayout.LabelField("Grid Bounds (with offset):", subHeaderStyle);
                EditorGUILayout.Vector3Field("Grid Center", gridBounds.center);
                EditorGUILayout.Vector3Field("Grid Size", gridBounds.size);
            }
            else
            {
                EditorGUILayout.HelpBox("No valid bounds available for testing", MessageType.Info);
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawMultiCellSection()
        {
            foldoutMultiCell = EditorGUILayout.Foldout(foldoutMultiCell, "Multi-Cell Operations", foldoutStyle);
            if (!foldoutMultiCell) return;

            EditorGUILayout.BeginVertical(boxStyle);

            EditorGUILayout.LabelField("Area Testing", subHeaderStyle);
            Vector3 areaCenter = EditorGUILayout.Vector3Field("Test Area Center", testArea.center);
            Vector3 areaSize = EditorGUILayout.Vector3Field("Test Area Size", testArea.size);
            testArea = new Bounds(areaCenter, areaSize);

            if (GUILayout.Button("Get Cells in Area") && hasValidBounds)
            {
                System.Collections.Generic.List<Vector2Int> cellsInArea;
                if (useGameObject && testGridContainer != null)
                {
                    GridUtility.TryGetBounds(testGridContainer, out var bounds);
                    cellsInArea = GridUtility.GetCellsInBounds(bounds, gridSize, testArea, selectedPlane, gridOffset);
                }
                else
                {
                    cellsInArea = GridUtility.GetCellsInBounds(customBounds, gridSize, testArea, selectedPlane, gridOffset);
                }

                Debug.Log($"Found {cellsInArea.Count} cells in area: {string.Join(", ", cellsInArea)}");
            }

            EditorGUILayout.LabelField("Batch Position Testing", subHeaderStyle);
            EditorGUILayout.LabelField($"Test cells: {string.Join(", ", testCellArray)}");

            if (GUILayout.Button("Get Multiple Positions") && hasValidBounds)
            {
                Vector3[] positions;
                if (useGameObject && testGridContainer != null)
                {
                    GridUtility.TryGetBounds(testGridContainer, out var bounds);
                    positions = GridUtility.GetWorldPositions(bounds, gridSize, testCellArray, selectedPlane, cellOffset, gridOffset);
                }
                else
                {
                    positions = GridUtility.GetWorldPositions(customBounds, gridSize, testCellArray, selectedPlane, cellOffset, gridOffset);
                }

                for (int i = 0; i < positions.Length; i++)
                {
                    Debug.Log($"Cell {testCellArray[i]} -> Position {positions[i]}");
                }
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawValidationSection()
        {
            foldoutValidation = EditorGUILayout.Foldout(foldoutValidation, "Grid Validation", foldoutStyle);
            if (!foldoutValidation) return;

            EditorGUILayout.BeginVertical(boxStyle);

            EditorGUILayout.LabelField("Cell Validation", subHeaderStyle);

            Vector2Int testCell = EditorGUILayout.Vector2IntField("Test Cell", selectedCell);
            bool isValid = GridUtility.IsValidCell(gridSize, testCell);
            EditorGUILayout.LabelField("Is Valid Cell", isValid.ToString());

            Vector2Int clampedCell = GridUtility.ClampToGrid(gridSize, testCell);
            EditorGUILayout.Vector2IntField("Clamped Cell", clampedCell);

            if (GUILayout.Button("Test Invalid Cell (-1, -1)"))
            {
                var invalidCell = new Vector2Int(-1, -1);
                bool valid = GridUtility.IsValidCell(gridSize, invalidCell);
                var clamped = GridUtility.ClampToGrid(gridSize, invalidCell);
                Debug.Log($"Cell {invalidCell}: Valid={valid}, Clamped={clamped}");
            }

            if (GUILayout.Button("Test Out-of-Bounds Cell (999, 999)"))
            {
                var oobCell = new Vector2Int(999, 999);
                bool valid = GridUtility.IsValidCell(gridSize, oobCell);
                var clamped = GridUtility.ClampToGrid(gridSize, oobCell);
                Debug.Log($"Cell {oobCell}: Valid={valid}, Clamped={clamped}");
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawDistanceSection()
        {
            foldoutDistance = EditorGUILayout.Foldout(foldoutDistance, "Distance & Pathfinding", foldoutStyle);
            if (!foldoutDistance) return;

            EditorGUILayout.BeginVertical(boxStyle);

            EditorGUILayout.LabelField("Distance Testing", subHeaderStyle);

            cellA = EditorGUILayout.Vector2IntField("Cell A", cellA);
            cellB = EditorGUILayout.Vector2IntField("Cell B", cellB);

            int distance = GridUtility.GetManhattanDistance(cellA, cellB);
            EditorGUILayout.LabelField("Manhattan Distance", distance.ToString());

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Radius Testing", subHeaderStyle);

            radiusTest = EditorGUILayout.IntField("Radius", radiusTest);

            if (GUILayout.Button("Get Cells in Radius"))
            {
                var cellsInRadius = GridUtility.GetCellsInRadius(cellA, radiusTest, gridSize);
                Debug.Log($"Cells within radius {radiusTest} of {cellA}: {string.Join(", ", cellsInRadius)}");
            }

            if (GUILayout.Button("Get Line Between Cells"))
            {
                var cellsInLine = GridUtility.GetCellsInLine(cellA, cellB, gridSize);
                Debug.Log($"Line from {cellA} to {cellB}: {string.Join(", ", cellsInLine)}");
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawVisualizationSection()
        {
            foldoutVisualization = EditorGUILayout.Foldout(foldoutVisualization, "Visualization", foldoutStyle);
            if (!foldoutVisualization) return;

            EditorGUILayout.BeginVertical(boxStyle);

            showGridVisualization = EditorGUILayout.Toggle("Show Grid", showGridVisualization);
            showCellHighlight = EditorGUILayout.Toggle("Highlight Selected Cell", showCellHighlight);
            gridColor = EditorGUILayout.ColorField("Grid Color", gridColor);
            cellHighlightColor = EditorGUILayout.ColorField("Cell Highlight Color", cellHighlightColor);

            EditorGUILayout.EndVertical();
        }

        private void DrawTestButtons()
        {
            EditorGUILayout.BeginVertical(boxStyle);

            EditorGUILayout.LabelField("Quick Tests", subHeaderStyle);

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Test Center"))
            {
                cellOffset = new Vector2(0.5f, 0.5f);
                CalculateResults();
            }
            if (GUILayout.Button("Test Corner"))
            {
                cellOffset = Vector2.zero;
                CalculateResults();
            }
            if (GUILayout.Button("Test Opposite Corner"))
            {
                cellOffset = Vector2.one;
                CalculateResults();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Create Test Cube"))
            {
                CreateTestCube();
            }
            if (GUILayout.Button("Focus on Grid"))
            {
                FocusOnGrid();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();
        }

        #endregion

        #region Calculation Methods

        private void CalculateResults()
        {
            lastError = "";
            hasValidBounds = false;

            try
            {
                if (useGameObject)
                {
                    if (testGridContainer == null)
                    {
                        lastError = "No GameObject selected";
                        return;
                    }

                    hasValidBounds = GridUtility.TryGetBounds(testGridContainer, out var bounds);
                    if (!hasValidBounds)
                    {
                        lastError = "GameObject has no valid bounds (Renderer/Collider required)";
                        return;
                    }

                    calculatedPosition = GridUtility.GetWorldPosition(testGridContainer, gridSize, selectedCell, selectedPlane, cellOffset, gridOffset);
                    calculatedCoords = GridUtility.GetGridCoordinates(testGridContainer, gridSize, calculatedPosition, selectedPlane, gridOffset);
                }
                else
                {
                    hasValidBounds = true;
                    calculatedPosition = GridUtility.GetWorldPosition(customBounds, gridSize, selectedCell, selectedPlane, cellOffset, gridOffset);
                    calculatedCoords = GridUtility.GetGridCoordinates(customBounds, gridSize, calculatedPosition, selectedPlane, gridOffset);
                }
            }
            catch (System.Exception ex)
            {
                lastError = ex.Message;
            }

            Repaint();
            SceneView.RepaintAll();
        }

        private void CalculateSnapResults()
        {
            CalculateSnapResults(cellOffset);
        }

        private void CalculateSnapResults(Vector2 snapCellOffset)
        {
            try
            {
                if (useGameObject && testGridContainer != null)
                {
                    snappedPosition = GridUtility.SnapToGrid(testGridContainer, gridSize, snapTestPosition, selectedPlane, snapCellOffset, gridOffset);
                }
                else if (!useGameObject)
                {
                    snappedPosition = GridUtility.SnapToGrid(customBounds, gridSize, snapTestPosition, selectedPlane, snapCellOffset, gridOffset);
                }
                else
                {
                    snappedPosition = snapTestPosition; // No valid grid, return original
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Error calculating snap results: {ex.Message}");
                snappedPosition = snapTestPosition;
            }

            Repaint();
            SceneView.RepaintAll();
        }

        #endregion

        #region Helper Methods

        private void CreateTestCube()
        {
            var cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cube.name = "Grid Test Container";
            cube.transform.position = Vector3.zero;
            cube.transform.localScale = Vector3.one * 10f;

            testGridContainer = cube;
            useGameObject = true;
            CalculateResults();

            Selection.activeGameObject = cube;
            SceneView.FrameLastActiveSceneView();
        }

        private void FocusOnGrid()
        {
            if (useGameObject && testGridContainer != null)
            {
                Selection.activeGameObject = testGridContainer;
                SceneView.FrameLastActiveSceneView();
            }
            else if (!useGameObject)
            {
                SceneView.lastActiveSceneView.LookAt(customBounds.center, SceneView.lastActiveSceneView.rotation, customBounds.size.magnitude);
            }
        }

        protected override void OnRefreshClicked()
        {
            CalculateResults();
            base.OnRefreshClicked();
        }

        protected override void OnClearClicked()
        {
            if (DisplayConfirmationDialog("Clear Test Data", "This will reset all test settings. Continue?", "Clear", "Cancel"))
            {
                testGridContainer = null;
                gridSize = new Vector2Int(5, 5);
                selectedCell = new Vector2Int(0, 0);
                cellOffset = new Vector2(0.5f, 0.5f);
                gridOffset = Vector3.zero;
                selectedPlane = GridUtility.GridPlane.XZ;
                customBounds = new Bounds(Vector3.zero, Vector3.one * 10f);
                useGameObject = true;
                lastError = "";
                snapTestPosition = Vector3.zero;
                snappedPosition = Vector3.zero;
                testArea = new Bounds(Vector3.zero, Vector3.one * 5f);
                testCellArray = new Vector2Int[] { new Vector2Int(0, 0), new Vector2Int(1, 1), new Vector2Int(2, 2) };
                cellA = new Vector2Int(0, 0);
                cellB = new Vector2Int(3, 4);
                radiusTest = 2;
                CalculateResults();
            }
        }

        #endregion

        #region Scene GUI

        private void OnSceneGUI(SceneView sceneView)
        {
            if (!showGridVisualization && !showCellHighlight) return;

            Bounds bounds;
            bool validBounds = false;

            if (useGameObject && testGridContainer != null)
            {
                validBounds = GridUtility.TryGetBounds(testGridContainer, out bounds);
            }
            else if (!useGameObject)
            {
                bounds = customBounds;
                validBounds = true;
            }
            else
            {
                return;
            }

            if (!validBounds) return;

            Handles.color = gridColor;

            if (showGridVisualization)
            {
                DrawGridVisualization(bounds);
            }

            if (showCellHighlight)
            {
                DrawCellHighlight(bounds);
            }

            // Always draw snap testing visualization if we have valid data
            DrawSnapVisualization(bounds);
        }

        private void DrawGridVisualization(Bounds bounds)
        {
            GridUtility.GetPlaneVectors(selectedPlane, out var uAxis, out var vAxis, out var wAxis);

            var planeSize = new Vector2(Vector3.Dot(bounds.size, uAxis), Vector3.Dot(bounds.size, vAxis));
            var cellSize = new Vector2(planeSize.x / gridSize.x, planeSize.y / gridSize.y);
            var gridOrigin = bounds.center - bounds.extents + gridOffset;

            // Draw grid lines
            for (int x = 0; x <= gridSize.x; x++)
            {
                var start = gridOrigin + uAxis * (cellSize.x * x) + wAxis * (Vector3.Dot(bounds.size, wAxis) * 0.5f);
                var end = start + vAxis * planeSize.y;
                Handles.DrawLine(start, end);
            }

            for (int y = 0; y <= gridSize.y; y++)
            {
                var start = gridOrigin + vAxis * (cellSize.y * y) + wAxis * (Vector3.Dot(bounds.size, wAxis) * 0.5f);
                var end = start + uAxis * planeSize.x;
                Handles.DrawLine(start, end);
            }
        }

        private void DrawCellHighlight(Bounds bounds)
        {
            Handles.color = cellHighlightColor;

            // Draw highlighted cell
            var position = useGameObject
                ? GridUtility.GetWorldPosition(testGridContainer, gridSize, selectedCell, selectedPlane, cellOffset, gridOffset)
                : GridUtility.GetWorldPosition(bounds, gridSize, selectedCell, selectedPlane, cellOffset, gridOffset);

            // Draw a sphere at the calculated position
            Handles.SphereHandleCap(0, position, Quaternion.identity, 0.2f, EventType.Repaint);

            // Draw cell bounds
            GridUtility.GetPlaneVectors(selectedPlane, out var uAxis, out var vAxis, out var wAxis);
            var planeSize = new Vector2(Vector3.Dot(bounds.size, uAxis), Vector3.Dot(bounds.size, vAxis));
            var cellSize = new Vector2(planeSize.x / gridSize.x, planeSize.y / gridSize.y);
            var gridOrigin = bounds.center - bounds.extents + gridOffset;

            var x = selectedCell.x;
            var y = selectedCell.y;
            if (GridUtility.UseOneBasedIndexing)
            {
                x -= 1;
                y -= 1;
            }

            var cellCorner = gridOrigin +
                uAxis * (cellSize.x * x) +
                vAxis * (cellSize.y * y) +
                wAxis * (Vector3.Dot(bounds.size, wAxis) * 0.5f);

            var corners = new Vector3[4]
            {
                cellCorner,
                cellCorner + uAxis * cellSize.x,
                cellCorner + uAxis * cellSize.x + vAxis * cellSize.y,
                cellCorner + vAxis * cellSize.y
            };

            // Draw cell outline
            for (int i = 0; i < 4; i++)
            {
                Handles.DrawLine(corners[i], corners[(i + 1) % 4]);
            }
        }

        private void DrawSnapVisualization(Bounds bounds)
        {
            if (snapTestPosition == Vector3.zero && snappedPosition == Vector3.zero) return;

            // Draw original position in yellow
            if (snapTestPosition != Vector3.zero)
            {
                Handles.color = Color.yellow;
                Handles.SphereHandleCap(0, snapTestPosition, Quaternion.identity, 0.15f, EventType.Repaint);
                Handles.Label(snapTestPosition + Vector3.up * 0.5f, "Original");
            }

            // Draw snapped position in green
            if (snappedPosition != Vector3.zero)
            {
                Handles.color = Color.green;
                Handles.SphereHandleCap(0, snappedPosition, Quaternion.identity, 0.15f, EventType.Repaint);
                Handles.Label(snappedPosition + Vector3.up * 0.5f, "Snapped");

                // Draw line between original and snapped positions
                if (snapTestPosition != Vector3.zero && snapTestPosition != snappedPosition)
                {
                    Handles.color = Color.cyan;
                    Handles.DrawDottedLine(snapTestPosition, snappedPosition, 5f);
                }
            }
        }

        #endregion
    }
}
