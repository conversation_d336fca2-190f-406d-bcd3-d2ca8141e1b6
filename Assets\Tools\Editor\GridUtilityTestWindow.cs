using UnityEditor;
using UnityEngine;
using SmartVertex.Tools;

namespace SmartVertex.EditorTools
{
    /// <summary>
    /// Editor window for testing GridUtility functionality in both 2D and 3D spaces.
    /// </summary>
    public class GridUtilityTestWindow : BaseDebugWindow
    {
        #region Fields

        private GameObject testGridContainer;
        private Vector2Int gridSize = new Vector2Int(5, 5);
        private Vector2Int selectedCell = new Vector2Int(0, 0);
        private Vector2 cellOffset = new Vector2(0.5f, 0.5f);
        private GridUtility.GridPlane selectedPlane = GridUtility.GridPlane.XZ;
        private Bounds customBounds = new Bounds(Vector3.zero, Vector3.one * 10f);
        private bool useGameObject = true;
        private bool showGridVisualization = true;
        private bool showCellHighlight = true;
        private Color gridColor = Color.white;
        private Color cellHighlightColor = Color.red;
        private bool foldoutSettings = true;
        private bool foldoutResults = true;
        private bool foldoutVisualization = true;

        // Test results
        private Vector3 calculatedPosition;
        private Vector2Int calculatedCoords;
        private bool hasValidBounds;
        private string lastError = "";

        #endregion

        #region Unity Editor Methods

        /// <summary>
        /// Shows the GridUtility Test window.
        /// </summary>
        public static void ShowWindow()
        {
            var window = GetWindow<GridUtilityTestWindow>("Grid Utility Tester");
            window.minSize = new Vector2(400, 600);
            window.Show();
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            SceneView.duringSceneGui += OnSceneGUI;
        }

        private void OnDisable()
        {
            SceneView.duringSceneGui -= OnSceneGUI;
        }

        private void OnGUI()
        {
            InitializeStyles();
            DrawHeader("Grid Utility Tester", "Refresh calculations", "Clear test data");

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            DrawSettingsSection();
            DrawResultsSection();
            DrawVisualizationSection();
            DrawTestButtons();

            EditorGUILayout.EndScrollView();
        }

        #endregion

        #region GUI Drawing Methods

        private void DrawSettingsSection()
        {
            foldoutSettings = EditorGUILayout.Foldout(foldoutSettings, "Grid Settings", foldoutStyle);
            if (!foldoutSettings) return;

            EditorGUILayout.BeginVertical(boxStyle);

            // Grid container selection
            useGameObject = EditorGUILayout.Toggle("Use GameObject", useGameObject);

            if (useGameObject)
            {
                EditorGUI.BeginChangeCheck();
                testGridContainer = (GameObject)EditorGUILayout.ObjectField(
                    "Grid Container", testGridContainer, typeof(GameObject), true);
                if (EditorGUI.EndChangeCheck())
                {
                    CalculateResults();
                }
            }
            else
            {
                EditorGUILayout.LabelField("Custom Bounds", subHeaderStyle);
                EditorGUI.BeginChangeCheck();
                Vector3 center = EditorGUILayout.Vector3Field("Center", customBounds.center);
                Vector3 size = EditorGUILayout.Vector3Field("Size", customBounds.size);
                if (EditorGUI.EndChangeCheck())
                {
                    customBounds = new Bounds(center, size);
                    CalculateResults();
                }
            }

            // Grid configuration
            EditorGUI.BeginChangeCheck();
            gridSize = EditorGUILayout.Vector2IntField("Grid Size", gridSize);
            selectedCell = EditorGUILayout.Vector2IntField("Selected Cell", selectedCell);
            cellOffset = EditorGUILayout.Vector2Field("Cell Offset", cellOffset);
            selectedPlane = (GridUtility.GridPlane)EditorGUILayout.EnumPopup("Grid Plane", selectedPlane);
            if (EditorGUI.EndChangeCheck())
            {
                CalculateResults();
            }

            // Indexing mode
            bool oneBasedIndexing = EditorGUILayout.Toggle("One-Based Indexing", GridUtility.UseOneBasedIndexing);
            if (oneBasedIndexing != GridUtility.UseOneBasedIndexing)
            {
                GridUtility.UseOneBasedIndexing = oneBasedIndexing;
                CalculateResults();
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawResultsSection()
        {
            foldoutResults = EditorGUILayout.Foldout(foldoutResults, "Test Results", foldoutStyle);
            if (!foldoutResults) return;

            EditorGUILayout.BeginVertical(boxStyle);

            if (!string.IsNullOrEmpty(lastError))
            {
                EditorGUILayout.HelpBox(lastError, MessageType.Error);
            }

            EditorGUILayout.LabelField("Has Valid Bounds", hasValidBounds.ToString());
            EditorGUILayout.Vector3Field("Calculated Position", calculatedPosition);
            EditorGUILayout.Vector2IntField("Calculated Coordinates", calculatedCoords);

            // Show bounds info
            if (useGameObject && testGridContainer != null)
            {
                if (GridUtility.TryGetBounds(testGridContainer, out var bounds))
                {
                    EditorGUILayout.LabelField("GameObject Bounds:", subHeaderStyle);
                    EditorGUILayout.Vector3Field("Center", bounds.center);
                    EditorGUILayout.Vector3Field("Size", bounds.size);
                }
            }
            else if (!useGameObject)
            {
                EditorGUILayout.LabelField("Custom Bounds:", subHeaderStyle);
                EditorGUILayout.Vector3Field("Center", customBounds.center);
                EditorGUILayout.Vector3Field("Size", customBounds.size);
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawVisualizationSection()
        {
            foldoutVisualization = EditorGUILayout.Foldout(foldoutVisualization, "Visualization", foldoutStyle);
            if (!foldoutVisualization) return;

            EditorGUILayout.BeginVertical(boxStyle);

            showGridVisualization = EditorGUILayout.Toggle("Show Grid", showGridVisualization);
            showCellHighlight = EditorGUILayout.Toggle("Highlight Selected Cell", showCellHighlight);
            gridColor = EditorGUILayout.ColorField("Grid Color", gridColor);
            cellHighlightColor = EditorGUILayout.ColorField("Cell Highlight Color", cellHighlightColor);

            EditorGUILayout.EndVertical();
        }

        private void DrawTestButtons()
        {
            EditorGUILayout.BeginVertical(boxStyle);

            EditorGUILayout.LabelField("Quick Tests", subHeaderStyle);

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Test Center"))
            {
                cellOffset = new Vector2(0.5f, 0.5f);
                CalculateResults();
            }
            if (GUILayout.Button("Test Corner"))
            {
                cellOffset = Vector2.zero;
                CalculateResults();
            }
            if (GUILayout.Button("Test Opposite Corner"))
            {
                cellOffset = Vector2.one;
                CalculateResults();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Create Test Cube"))
            {
                CreateTestCube();
            }
            if (GUILayout.Button("Focus on Grid"))
            {
                FocusOnGrid();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();
        }

        #endregion

        #region Calculation Methods

        private void CalculateResults()
        {
            lastError = "";
            hasValidBounds = false;

            try
            {
                if (useGameObject)
                {
                    if (testGridContainer == null)
                    {
                        lastError = "No GameObject selected";
                        return;
                    }

                    hasValidBounds = GridUtility.TryGetBounds(testGridContainer, out var bounds);
                    if (!hasValidBounds)
                    {
                        lastError = "GameObject has no valid bounds (Renderer/Collider required)";
                        return;
                    }

                    calculatedPosition = GridUtility.GetWorldPosition(testGridContainer, gridSize, selectedCell, selectedPlane, cellOffset);
                    calculatedCoords = GridUtility.GetGridCoordinates(testGridContainer, gridSize, calculatedPosition, selectedPlane);
                }
                else
                {
                    hasValidBounds = true;
                    calculatedPosition = GridUtility.GetWorldPosition(customBounds, gridSize, selectedCell, selectedPlane, cellOffset);
                    calculatedCoords = GridUtility.GetGridCoordinates(customBounds, gridSize, calculatedPosition, selectedPlane);
                }
            }
            catch (System.Exception ex)
            {
                lastError = ex.Message;
            }

            Repaint();
            SceneView.RepaintAll();
        }

        #endregion

        #region Helper Methods

        private void CreateTestCube()
        {
            var cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cube.name = "Grid Test Container";
            cube.transform.position = Vector3.zero;
            cube.transform.localScale = Vector3.one * 10f;

            testGridContainer = cube;
            useGameObject = true;
            CalculateResults();

            Selection.activeGameObject = cube;
            SceneView.FrameLastActiveSceneView();
        }

        private void FocusOnGrid()
        {
            if (useGameObject && testGridContainer != null)
            {
                Selection.activeGameObject = testGridContainer;
                SceneView.FrameLastActiveSceneView();
            }
            else if (!useGameObject)
            {
                SceneView.lastActiveSceneView.LookAt(customBounds.center, SceneView.lastActiveSceneView.rotation, customBounds.size.magnitude);
            }
        }

        protected override void OnRefreshClicked()
        {
            CalculateResults();
            base.OnRefreshClicked();
        }

        protected override void OnClearClicked()
        {
            if (DisplayConfirmationDialog("Clear Test Data", "This will reset all test settings. Continue?", "Clear", "Cancel"))
            {
                testGridContainer = null;
                gridSize = new Vector2Int(5, 5);
                selectedCell = new Vector2Int(0, 0);
                cellOffset = new Vector2(0.5f, 0.5f);
                selectedPlane = GridUtility.GridPlane.XZ;
                customBounds = new Bounds(Vector3.zero, Vector3.one * 10f);
                useGameObject = true;
                lastError = "";
                CalculateResults();
            }
        }

        #endregion

        #region Scene GUI

        private void OnSceneGUI(SceneView sceneView)
        {
            if (!showGridVisualization && !showCellHighlight) return;

            Bounds bounds;
            bool validBounds = false;

            if (useGameObject && testGridContainer != null)
            {
                validBounds = GridUtility.TryGetBounds(testGridContainer, out bounds);
            }
            else if (!useGameObject)
            {
                bounds = customBounds;
                validBounds = true;
            }
            else
            {
                return;
            }

            if (!validBounds) return;

            Handles.color = gridColor;

            if (showGridVisualization)
            {
                DrawGridVisualization(bounds);
            }

            if (showCellHighlight)
            {
                DrawCellHighlight(bounds);
            }
        }

        private void DrawGridVisualization(Bounds bounds)
        {
            GridUtility.GetPlaneVectors(selectedPlane, out var uAxis, out var vAxis, out var wAxis);

            var planeSize = new Vector2(Vector3.Dot(bounds.size, uAxis), Vector3.Dot(bounds.size, vAxis));
            var cellSize = new Vector2(planeSize.x / gridSize.x, planeSize.y / gridSize.y);
            var gridOrigin = bounds.center - bounds.extents;

            // Draw grid lines
            for (int x = 0; x <= gridSize.x; x++)
            {
                var start = gridOrigin + uAxis * (cellSize.x * x) + wAxis * (Vector3.Dot(bounds.size, wAxis) * 0.5f);
                var end = start + vAxis * planeSize.y;
                Handles.DrawLine(start, end);
            }

            for (int y = 0; y <= gridSize.y; y++)
            {
                var start = gridOrigin + vAxis * (cellSize.y * y) + wAxis * (Vector3.Dot(bounds.size, wAxis) * 0.5f);
                var end = start + uAxis * planeSize.x;
                Handles.DrawLine(start, end);
            }
        }

        private void DrawCellHighlight(Bounds bounds)
        {
            Handles.color = cellHighlightColor;

            // Draw highlighted cell
            var position = useGameObject
                ? GridUtility.GetWorldPosition(testGridContainer, gridSize, selectedCell, selectedPlane, cellOffset)
                : GridUtility.GetWorldPosition(bounds, gridSize, selectedCell, selectedPlane, cellOffset);

            // Draw a sphere at the calculated position
            Handles.SphereHandleCap(0, position, Quaternion.identity, 0.2f, EventType.Repaint);

            // Draw cell bounds
            GridUtility.GetPlaneVectors(selectedPlane, out var uAxis, out var vAxis, out var wAxis);
            var planeSize = new Vector2(Vector3.Dot(bounds.size, uAxis), Vector3.Dot(bounds.size, vAxis));
            var cellSize = new Vector2(planeSize.x / gridSize.x, planeSize.y / gridSize.y);
            var gridOrigin = bounds.center - bounds.extents;

            var x = selectedCell.x;
            var y = selectedCell.y;
            if (GridUtility.UseOneBasedIndexing)
            {
                x -= 1;
                y -= 1;
            }

            var cellCorner = gridOrigin +
                uAxis * (cellSize.x * x) +
                vAxis * (cellSize.y * y) +
                wAxis * (Vector3.Dot(bounds.size, wAxis) * 0.5f);

            var corners = new Vector3[4]
            {
                cellCorner,
                cellCorner + uAxis * cellSize.x,
                cellCorner + uAxis * cellSize.x + vAxis * cellSize.y,
                cellCorner + vAxis * cellSize.y
            };

            // Draw cell outline
            for (int i = 0; i < 4; i++)
            {
                Handles.DrawLine(corners[i], corners[(i + 1) % 4]);
            }
        }

        #endregion
    }
}
