using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Provides utility methods for grid calculations in both 2D and 3D space.
    /// </summary>
    public static class GridUtility
    {
        /// <summary>
        /// Defines the 2D plane onto which the grid is projected.
        /// </summary>
        public enum GridPlane
        {
            XY = 0,
            XZ = 1,
            YZ = 2
        }

        /// <summary>
        /// If true, grid coordinates are 1-based (start from 1). If false, grid coordinates are 0-based (default).
        /// </summary>
        public static bool UseOneBasedIndexing { get; set; } = false;

        /// <summary>
        /// Calculates the world position for the center of a 2D grid cell, projected onto a specified plane.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The 0-indexed coordinates of the target cell. For XZ plane, Y corresponds to the Z axis.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <returns>The world space position for the center of the specified cell.</returns>
        public static Vector3 GetWorldPosition(Bounds gridBounds, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane)
        {
            return GetWorldPosition(gridBounds, gridSize, cellCoords, plane, new Vector2(0.5f, 0.5f));
        }

        /// <summary>
        /// Calculates the world position for a 2D grid cell, projected onto a specified plane.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The 0-indexed coordinates of the target cell. For XZ plane, Y corresponds to the Z axis.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="cellOffset">The offset within the cell (0,0 = bottom-left corner, 0.5,0.5 = center, 1,1 = top-right corner).</param>
        /// <returns>The world space position for the specified position within the cell.</returns>
        public static Vector3 GetWorldPosition(Bounds gridBounds, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane, Vector2 cellOffset)
        {
            return GetWorldPosition(gridBounds, gridSize, cellCoords, plane, cellOffset, Vector3.zero);
        }

        /// <summary>
        /// Calculates the world position for a 2D grid cell, projected onto a specified plane, with grid positioning offset.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The 0-indexed coordinates of the target cell. For XZ plane, Y corresponds to the Z axis.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="cellOffset">The offset within the cell (0,0 = bottom-left corner, 0.5,0.5 = center, 1,1 = top-right corner).</param>
        /// <param name="gridOffset">World space offset to apply to the entire grid position. Allows moving the grid relative to the bounds center.</param>
        /// <returns>The world space position for the specified position within the cell.</returns>
        public static Vector3 GetWorldPosition(Bounds gridBounds, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane, Vector2 cellOffset, Vector3 gridOffset)
        {
            if (gridSize.x <= 0 || gridSize.y <= 0)
            {
                Debug.LogError("Grid size must have positive dimensions.");
                return gridBounds.center;
            }

            GetPlaneVectors(plane, out var uAxis, out var vAxis, out var wAxis);
            var planeSize = new Vector2(Vector3.Dot(gridBounds.size, uAxis), Vector3.Dot(gridBounds.size, vAxis));
            var cellSize = new Vector2(planeSize.x / gridSize.x, planeSize.y / gridSize.y);
            var gridOrigin = gridBounds.center - gridBounds.extents + gridOffset;

            var x = cellCoords.x;
            var y = cellCoords.y;
            if (UseOneBasedIndexing)
            {
                x -= 1;
                y -= 1;
            }

            var position = gridOrigin +
                uAxis * (cellSize.x * (x + cellOffset.x)) +
                vAxis * (cellSize.y * (y + cellOffset.y)) +
                wAxis * (Vector3.Dot(gridBounds.size, wAxis) * 0.5f);

            return position;
        }

        /// <summary>
        /// Calculates which 2D grid cell contains a given world position on a specified plane.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldPosition">The world position to check.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <returns>The coordinates of the grid cell. Returns (-1, -1) if the position is outside the grid bounds.</returns>
        public static Vector2Int GetGridCoordinates(Bounds gridBounds, Vector2Int gridSize, Vector3 worldPosition, GridPlane plane)
        {
            return GetGridCoordinates(gridBounds, gridSize, worldPosition, plane, Vector3.zero);
        }

        /// <summary>
        /// Calculates which 2D grid cell contains a given world position on a specified plane, with grid positioning offset.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldPosition">The world position to check.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="gridOffset">World space offset that was applied to the grid position. Must match the offset used in GetWorldPosition.</param>
        /// <returns>The coordinates of the grid cell. Returns (-1, -1) if the position is outside the grid bounds.</returns>
        public static Vector2Int GetGridCoordinates(Bounds gridBounds, Vector2Int gridSize, Vector3 worldPosition, GridPlane plane, Vector3 gridOffset)
        {
            // Adjust bounds to account for grid offset when checking containment
            var adjustedBounds = new Bounds(gridBounds.center + gridOffset, gridBounds.size);
            if (!adjustedBounds.Contains(worldPosition))
                return new Vector2Int(-1, -1);

            GetPlaneVectors(plane, out var uAxis, out var vAxis, out _);
            var planeSize = new Vector2(Vector3.Dot(gridBounds.size, uAxis), Vector3.Dot(gridBounds.size, vAxis));
            var cellSize = new Vector2(planeSize.x / gridSize.x, planeSize.y / gridSize.y);
            var gridOrigin = gridBounds.center - gridBounds.extents + gridOffset;
            var relativePos = worldPosition - gridOrigin;

            var x = Mathf.FloorToInt(Vector3.Dot(relativePos, uAxis) / cellSize.x);
            var y = Mathf.FloorToInt(Vector3.Dot(relativePos, vAxis) / cellSize.y);

            if (UseOneBasedIndexing)
            {
                x += 1;
                y += 1;
            }

            return new Vector2Int(x, y);
        }

        /// <summary>
        /// Calculates the world position for the center of a 2D grid cell using a GameObject as the grid container.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The 0-indexed coordinates of the target cell.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <returns>The world space position for the center of the specified cell, or Vector3.zero if bounds cannot be determined.</returns>
        public static Vector3 GetWorldPosition(GameObject gridContainer, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? GetWorldPosition(bounds, gridSize, cellCoords, plane)
                : Vector3.zero;
        }

        /// <summary>
        /// Calculates the world position for a 2D grid cell using a GameObject as the grid container.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The 0-indexed coordinates of the target cell.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="cellOffset">The offset within the cell (0,0 = bottom-left corner, 0.5,0.5 = center, 1,1 = top-right corner).</param>
        /// <returns>The world space position for the specified position within the cell, or Vector3.zero if bounds cannot be determined.</returns>
        public static Vector3 GetWorldPosition(GameObject gridContainer, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane, Vector2 cellOffset)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? GetWorldPosition(bounds, gridSize, cellCoords, plane, cellOffset)
                : Vector3.zero;
        }

        /// <summary>
        /// Calculates the world position for a 2D grid cell using a GameObject as the grid container, with grid positioning offset.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The 0-indexed coordinates of the target cell.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="cellOffset">The offset within the cell (0,0 = bottom-left corner, 0.5,0.5 = center, 1,1 = top-right corner).</param>
        /// <param name="gridOffset">World space offset to apply to the entire grid position. Allows moving the grid relative to the bounds center.</param>
        /// <returns>The world space position for the specified position within the cell, or Vector3.zero if bounds cannot be determined.</returns>
        public static Vector3 GetWorldPosition(GameObject gridContainer, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane, Vector2 cellOffset, Vector3 gridOffset)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? GetWorldPosition(bounds, gridSize, cellCoords, plane, cellOffset, gridOffset)
                : Vector3.zero;
        }

        /// <summary>
        /// Calculates which 2D grid cell contains a given world position using a GameObject as the grid container.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldPosition">The world position to check.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <returns>The coordinates of the grid cell. Returns (-1, -1) if the position is outside the grid bounds or bounds cannot be determined.</returns>
        public static Vector2Int GetGridCoordinates(GameObject gridContainer, Vector2Int gridSize, Vector3 worldPosition, GridPlane plane)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? GetGridCoordinates(bounds, gridSize, worldPosition, plane)
                : new Vector2Int(-1, -1);
        }

        /// <summary>
        /// Calculates which 2D grid cell contains a given world position using a GameObject as the grid container, with grid positioning offset.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldPosition">The world position to check.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="gridOffset">World space offset that was applied to the grid position. Must match the offset used in GetWorldPosition.</param>
        /// <returns>The coordinates of the grid cell. Returns (-1, -1) if the position is outside the grid bounds or bounds cannot be determined.</returns>
        public static Vector2Int GetGridCoordinates(GameObject gridContainer, Vector2Int gridSize, Vector3 worldPosition, GridPlane plane, Vector3 gridOffset)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? GetGridCoordinates(bounds, gridSize, worldPosition, plane, gridOffset)
                : new Vector2Int(-1, -1);
        }

        /// <summary>
        /// Gets the primary (u), secondary (v), and normal (w) axes for a given plane.
        /// </summary>
        /// <param name="plane">The grid plane.</param>
        /// <param name="u">Primary axis (grid X).</param>
        /// <param name="v">Secondary axis (grid Y).</param>
        /// <param name="w">Normal axis (depth).</param>
        public static void GetPlaneVectors(GridPlane plane, out Vector3 u, out Vector3 v, out Vector3 w)
        {
            switch (plane)
            {
                case GridPlane.XZ:
                    u = Vector3.right;
                    v = Vector3.forward;
                    w = Vector3.up;
                    break;
                case GridPlane.YZ:
                    u = Vector3.up;
                    v = Vector3.forward;
                    w = Vector3.right;
                    break;
                case GridPlane.XY:
                default:
                    u = Vector3.right;
                    v = Vector3.up;
                    w = Vector3.forward;
                    break;
            }
        }

        /// <summary>
        /// Tries to get the bounding box of a GameObject by looking for a Renderer or Collider.
        /// </summary>
        /// <param name="go">The GameObject to check.</param>
        /// <param name="bounds">The resulting bounds if found.</param>
        /// <returns>True if bounds were found, otherwise false.</returns>
        public static bool TryGetBounds(GameObject go, out Bounds bounds)
        {
            if (go == null)
            {
                bounds = default;
                return false;
            }
            if (go.TryGetComponent<Renderer>(out var renderer))
            {
                bounds = renderer.bounds;
                return true;
            }
            if (go.TryGetComponent<Collider>(out var collider))
            {
                bounds = collider.bounds;
                return true;
            }
            if (go.TryGetComponent<Collider2D>(out var collider2D))
            {
                bounds = collider2D.bounds;
                return true;
            }
            Debug.LogError($"Could not find a Renderer, Collider, or Collider2D on GameObject '{go.name}' to determine its bounds.", go);
            bounds = default;
            return false;
        }
    }
}