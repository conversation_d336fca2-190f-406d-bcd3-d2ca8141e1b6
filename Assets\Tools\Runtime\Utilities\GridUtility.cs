using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Provides utility methods for grid calculations in both 2D and 3D space.
    /// </summary>
    public static class GridUtility
    {
        /// <summary>
        /// Defines the 2D plane onto which the grid is projected.
        /// </summary>
        public enum GridPlane
        {
            XY = 0,
            XZ = 1,
            YZ = 2
        }

        /// <summary>
        /// If true, grid coordinates are 1-based (start from 1). If false, grid coordinates are 0-based (default).
        /// </summary>
        public static bool UseOneBasedIndexing { get; set; } = false;

        /// <summary>
        /// Calculates the world position for the center of a 2D grid cell, projected onto a specified plane.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The 0-indexed coordinates of the target cell. For XZ plane, Y corresponds to the Z axis.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <returns>The world space position for the center of the specified cell.</returns>
        public static Vector3 GetWorldPosition(Bounds gridBounds, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane)
        {
            return GetWorldPosition(gridBounds, gridSize, cellCoords, plane, new Vector2(0.5f, 0.5f));
        }

        /// <summary>
        /// Calculates the world position for a 2D grid cell, projected onto a specified plane.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The 0-indexed coordinates of the target cell. For XZ plane, Y corresponds to the Z axis.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="cellOffset">The offset within the cell (0,0 = bottom-left corner, 0.5,0.5 = center, 1,1 = top-right corner).</param>
        /// <returns>The world space position for the specified position within the cell.</returns>
        public static Vector3 GetWorldPosition(Bounds gridBounds, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane, Vector2 cellOffset)
        {
            return GetWorldPosition(gridBounds, gridSize, cellCoords, plane, cellOffset, Vector3.zero);
        }

        /// <summary>
        /// Calculates the world position for a 2D grid cell, projected onto a specified plane, with grid positioning offset.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The 0-indexed coordinates of the target cell. For XZ plane, Y corresponds to the Z axis.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="cellOffset">The offset within the cell (0,0 = bottom-left corner, 0.5,0.5 = center, 1,1 = top-right corner).</param>
        /// <param name="gridOffset">World space offset to apply to the entire grid position. Allows moving the grid relative to the bounds center.</param>
        /// <returns>The world space position for the specified position within the cell.</returns>
        public static Vector3 GetWorldPosition(Bounds gridBounds, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane, Vector2 cellOffset, Vector3 gridOffset)
        {
            if (gridSize.x <= 0 || gridSize.y <= 0)
            {
                Debug.LogError("Grid size must have positive dimensions.");
                return gridBounds.center;
            }

            GetPlaneVectors(plane, out var uAxis, out var vAxis, out var wAxis);
            var planeSize = new Vector2(Vector3.Dot(gridBounds.size, uAxis), Vector3.Dot(gridBounds.size, vAxis));
            var cellSize = new Vector2(planeSize.x / gridSize.x, planeSize.y / gridSize.y);
            var gridOrigin = gridBounds.center - gridBounds.extents + gridOffset;

            var x = cellCoords.x;
            var y = cellCoords.y;
            if (UseOneBasedIndexing)
            {
                x -= 1;
                y -= 1;
            }

            var position = gridOrigin +
                uAxis * (cellSize.x * (x + cellOffset.x)) +
                vAxis * (cellSize.y * (y + cellOffset.y)) +
                wAxis * (Vector3.Dot(gridBounds.size, wAxis) * 0.5f);

            return position;
        }

        /// <summary>
        /// Calculates which 2D grid cell contains a given world position on a specified plane.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldPosition">The world position to check.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <returns>The coordinates of the grid cell. Returns (-1, -1) if the position is outside the grid bounds.</returns>
        public static Vector2Int GetGridCoordinates(Bounds gridBounds, Vector2Int gridSize, Vector3 worldPosition, GridPlane plane)
        {
            return GetGridCoordinates(gridBounds, gridSize, worldPosition, plane, Vector3.zero);
        }

        /// <summary>
        /// Calculates which 2D grid cell contains a given world position on a specified plane, with grid positioning offset.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldPosition">The world position to check.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="gridOffset">World space offset that was applied to the grid position. Must match the offset used in GetWorldPosition.</param>
        /// <returns>The coordinates of the grid cell. Returns (-1, -1) if the position is outside the grid bounds.</returns>
        public static Vector2Int GetGridCoordinates(Bounds gridBounds, Vector2Int gridSize, Vector3 worldPosition, GridPlane plane, Vector3 gridOffset)
        {
            // Adjust bounds to account for grid offset when checking containment
            var adjustedBounds = new Bounds(gridBounds.center + gridOffset, gridBounds.size);
            if (!adjustedBounds.Contains(worldPosition))
                return new Vector2Int(-1, -1);

            GetPlaneVectors(plane, out var uAxis, out var vAxis, out _);
            var planeSize = new Vector2(Vector3.Dot(gridBounds.size, uAxis), Vector3.Dot(gridBounds.size, vAxis));
            var cellSize = new Vector2(planeSize.x / gridSize.x, planeSize.y / gridSize.y);
            var gridOrigin = gridBounds.center - gridBounds.extents + gridOffset;
            var relativePos = worldPosition - gridOrigin;

            var x = Mathf.FloorToInt(Vector3.Dot(relativePos, uAxis) / cellSize.x);
            var y = Mathf.FloorToInt(Vector3.Dot(relativePos, vAxis) / cellSize.y);

            if (UseOneBasedIndexing)
            {
                x += 1;
                y += 1;
            }

            return new Vector2Int(x, y);
        }

        /// <summary>
        /// Calculates the world position for the center of a 2D grid cell using a GameObject as the grid container.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The 0-indexed coordinates of the target cell.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <returns>The world space position for the center of the specified cell, or Vector3.zero if bounds cannot be determined.</returns>
        public static Vector3 GetWorldPosition(GameObject gridContainer, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? GetWorldPosition(bounds, gridSize, cellCoords, plane)
                : Vector3.zero;
        }

        /// <summary>
        /// Calculates the world position for a 2D grid cell using a GameObject as the grid container.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The 0-indexed coordinates of the target cell.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="cellOffset">The offset within the cell (0,0 = bottom-left corner, 0.5,0.5 = center, 1,1 = top-right corner).</param>
        /// <returns>The world space position for the specified position within the cell, or Vector3.zero if bounds cannot be determined.</returns>
        public static Vector3 GetWorldPosition(GameObject gridContainer, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane, Vector2 cellOffset)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? GetWorldPosition(bounds, gridSize, cellCoords, plane, cellOffset)
                : Vector3.zero;
        }

        /// <summary>
        /// Calculates the world position for a 2D grid cell using a GameObject as the grid container, with grid positioning offset.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The 0-indexed coordinates of the target cell.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="cellOffset">The offset within the cell (0,0 = bottom-left corner, 0.5,0.5 = center, 1,1 = top-right corner).</param>
        /// <param name="gridOffset">World space offset to apply to the entire grid position. Allows moving the grid relative to the bounds center.</param>
        /// <returns>The world space position for the specified position within the cell, or Vector3.zero if bounds cannot be determined.</returns>
        public static Vector3 GetWorldPosition(GameObject gridContainer, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane, Vector2 cellOffset, Vector3 gridOffset)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? GetWorldPosition(bounds, gridSize, cellCoords, plane, cellOffset, gridOffset)
                : Vector3.zero;
        }

        /// <summary>
        /// Calculates which 2D grid cell contains a given world position using a GameObject as the grid container.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldPosition">The world position to check.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <returns>The coordinates of the grid cell. Returns (-1, -1) if the position is outside the grid bounds or bounds cannot be determined.</returns>
        public static Vector2Int GetGridCoordinates(GameObject gridContainer, Vector2Int gridSize, Vector3 worldPosition, GridPlane plane)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? GetGridCoordinates(bounds, gridSize, worldPosition, plane)
                : new Vector2Int(-1, -1);
        }

        /// <summary>
        /// Calculates which 2D grid cell contains a given world position using a GameObject as the grid container, with grid positioning offset.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldPosition">The world position to check.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="gridOffset">World space offset that was applied to the grid position. Must match the offset used in GetWorldPosition.</param>
        /// <returns>The coordinates of the grid cell. Returns (-1, -1) if the position is outside the grid bounds or bounds cannot be determined.</returns>
        public static Vector2Int GetGridCoordinates(GameObject gridContainer, Vector2Int gridSize, Vector3 worldPosition, GridPlane plane, Vector3 gridOffset)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? GetGridCoordinates(bounds, gridSize, worldPosition, plane, gridOffset)
                : new Vector2Int(-1, -1);
        }

        /// <summary>
        /// Snaps a world position to the nearest grid cell position.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldPosition">The world position to snap to the grid.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <returns>The snapped world position at the center of the nearest grid cell, or the original position if outside grid bounds.</returns>
        public static Vector3 SnapToGrid(Bounds gridBounds, Vector2Int gridSize, Vector3 worldPosition, GridPlane plane)
        {
            return SnapToGrid(gridBounds, gridSize, worldPosition, plane, new Vector2(0.5f, 0.5f));
        }

        /// <summary>
        /// Snaps a world position to the nearest grid cell position with custom cell offset.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldPosition">The world position to snap to the grid.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="cellOffset">The offset within the cell (0,0 = bottom-left corner, 0.5,0.5 = center, 1,1 = top-right corner).</param>
        /// <returns>The snapped world position at the specified offset within the nearest grid cell, or the original position if outside grid bounds.</returns>
        public static Vector3 SnapToGrid(Bounds gridBounds, Vector2Int gridSize, Vector3 worldPosition, GridPlane plane, Vector2 cellOffset)
        {
            return SnapToGrid(gridBounds, gridSize, worldPosition, plane, cellOffset, Vector3.zero);
        }

        /// <summary>
        /// Snaps a world position to the nearest grid cell position with custom cell offset and grid positioning offset.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldPosition">The world position to snap to the grid.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="cellOffset">The offset within the cell (0,0 = bottom-left corner, 0.5,0.5 = center, 1,1 = top-right corner).</param>
        /// <param name="gridOffset">World space offset that was applied to the grid position. Must match the offset used in GetWorldPosition.</param>
        /// <returns>The snapped world position at the specified offset within the nearest grid cell, or the original position if outside grid bounds.</returns>
        public static Vector3 SnapToGrid(Bounds gridBounds, Vector2Int gridSize, Vector3 worldPosition, GridPlane plane, Vector2 cellOffset, Vector3 gridOffset)
        {
            var cellCoords = GetGridCoordinates(gridBounds, gridSize, worldPosition, plane, gridOffset);
            if (cellCoords.x == -1)
                return worldPosition; // Outside grid bounds, return original position

            return GetWorldPosition(gridBounds, gridSize, cellCoords, plane, cellOffset, gridOffset);
        }

        /// <summary>
        /// Snaps a world position to the nearest grid cell position using a GameObject as the grid container.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldPosition">The world position to snap to the grid.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <returns>The snapped world position at the center of the nearest grid cell, or the original position if outside grid bounds or bounds cannot be determined.</returns>
        public static Vector3 SnapToGrid(GameObject gridContainer, Vector2Int gridSize, Vector3 worldPosition, GridPlane plane)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? SnapToGrid(bounds, gridSize, worldPosition, plane)
                : worldPosition;
        }

        /// <summary>
        /// Snaps a world position to the nearest grid cell position using a GameObject as the grid container with custom cell offset.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldPosition">The world position to snap to the grid.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="cellOffset">The offset within the cell (0,0 = bottom-left corner, 0.5,0.5 = center, 1,1 = top-right corner).</param>
        /// <returns>The snapped world position at the specified offset within the nearest grid cell, or the original position if outside grid bounds or bounds cannot be determined.</returns>
        public static Vector3 SnapToGrid(GameObject gridContainer, Vector2Int gridSize, Vector3 worldPosition, GridPlane plane, Vector2 cellOffset)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? SnapToGrid(bounds, gridSize, worldPosition, plane, cellOffset)
                : worldPosition;
        }

        /// <summary>
        /// Snaps a world position to the nearest grid cell position using a GameObject as the grid container with custom cell offset and grid positioning offset.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldPosition">The world position to snap to the grid.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="cellOffset">The offset within the cell (0,0 = bottom-left corner, 0.5,0.5 = center, 1,1 = top-right corner).</param>
        /// <param name="gridOffset">World space offset that was applied to the grid position. Must match the offset used in GetWorldPosition.</param>
        /// <returns>The snapped world position at the specified offset within the nearest grid cell, or the original position if outside grid bounds or bounds cannot be determined.</returns>
        public static Vector3 SnapToGrid(GameObject gridContainer, Vector2Int gridSize, Vector3 worldPosition, GridPlane plane, Vector2 cellOffset, Vector3 gridOffset)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? SnapToGrid(bounds, gridSize, worldPosition, plane, cellOffset, gridOffset)
                : worldPosition;
        }

        /// <summary>
        /// Gets the world bounds of a specific grid cell.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The coordinates of the target cell.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <returns>The world space bounds of the specified cell.</returns>
        public static Bounds GetCellBounds(Bounds gridBounds, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane)
        {
            return GetCellBounds(gridBounds, gridSize, cellCoords, plane, Vector3.zero);
        }

        /// <summary>
        /// Gets the world bounds of a specific grid cell with grid positioning offset.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The coordinates of the target cell.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="gridOffset">World space offset that was applied to the grid position.</param>
        /// <returns>The world space bounds of the specified cell.</returns>
        public static Bounds GetCellBounds(Bounds gridBounds, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane, Vector3 gridOffset)
        {
            if (gridSize.x <= 0 || gridSize.y <= 0)
            {
                Debug.LogError("Grid size must have positive dimensions.");
                return new Bounds(gridBounds.center, Vector3.zero);
            }

            GetPlaneVectors(plane, out var uAxis, out var vAxis, out var wAxis);
            var planeSize = new Vector2(Vector3.Dot(gridBounds.size, uAxis), Vector3.Dot(gridBounds.size, vAxis));
            var cellSize = new Vector2(planeSize.x / gridSize.x, planeSize.y / gridSize.y);

            // Get cell center position
            var cellCenter = GetWorldPosition(gridBounds, gridSize, cellCoords, plane, new Vector2(0.5f, 0.5f), gridOffset);

            // Calculate cell bounds size
            var cellBoundsSize = uAxis * cellSize.x + vAxis * cellSize.y + wAxis * Vector3.Dot(gridBounds.size, wAxis);

            return new Bounds(cellCenter, cellBoundsSize);
        }

        /// <summary>
        /// Gets the world bounds of a specific grid cell using a GameObject as the grid container.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The coordinates of the target cell.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <returns>The world space bounds of the specified cell, or empty bounds if bounds cannot be determined.</returns>
        public static Bounds GetCellBounds(GameObject gridContainer, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? GetCellBounds(bounds, gridSize, cellCoords, plane)
                : new Bounds();
        }

        /// <summary>
        /// Gets the world bounds of a specific grid cell using a GameObject as the grid container with grid positioning offset.
        /// </summary>
        /// <param name="gridContainer">The GameObject whose bounds define the grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The coordinates of the target cell.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="gridOffset">World space offset that was applied to the grid position.</param>
        /// <returns>The world space bounds of the specified cell, or empty bounds if bounds cannot be determined.</returns>
        public static Bounds GetCellBounds(GameObject gridContainer, Vector2Int gridSize, Vector2Int cellCoords, GridPlane plane, Vector3 gridOffset)
        {
            return TryGetBounds(gridContainer, out var bounds)
                ? GetCellBounds(bounds, gridSize, cellCoords, plane, gridOffset)
                : new Bounds();
        }

        /// <summary>
        /// Gets the total world bounds of the entire grid (useful when grid is offset from original bounds).
        /// </summary>
        /// <param name="originalBounds">The original bounds used as reference.</param>
        /// <param name="gridOffset">World space offset applied to the grid position.</param>
        /// <returns>The world space bounds of the entire grid including offset.</returns>
        public static Bounds GetGridBounds(Bounds originalBounds, Vector3 gridOffset)
        {
            return new Bounds(originalBounds.center + gridOffset, originalBounds.size);
        }

        /// <summary>
        /// Gets all cell coordinates within a world space area.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldArea">The world space area to check for overlapping cells.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <returns>List of cell coordinates that intersect with the world area.</returns>
        public static System.Collections.Generic.List<Vector2Int> GetCellsInBounds(Bounds gridBounds, Vector2Int gridSize, Bounds worldArea, GridPlane plane)
        {
            return GetCellsInBounds(gridBounds, gridSize, worldArea, plane, Vector3.zero);
        }

        /// <summary>
        /// Gets all cell coordinates within a world space area with grid positioning offset.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="worldArea">The world space area to check for overlapping cells.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="gridOffset">World space offset that was applied to the grid position.</param>
        /// <returns>List of cell coordinates that intersect with the world area.</returns>
        public static System.Collections.Generic.List<Vector2Int> GetCellsInBounds(Bounds gridBounds, Vector2Int gridSize, Bounds worldArea, GridPlane plane, Vector3 gridOffset)
        {
            var result = new System.Collections.Generic.List<Vector2Int>();

            if (gridSize.x <= 0 || gridSize.y <= 0)
            {
                Debug.LogError("Grid size must have positive dimensions.");
                return result;
            }

            // Get the corners of the world area and find the cell range
            var minCorner = worldArea.min;
            var maxCorner = worldArea.max;

            var minCell = GetGridCoordinates(gridBounds, gridSize, minCorner, plane, gridOffset);
            var maxCell = GetGridCoordinates(gridBounds, gridSize, maxCorner, plane, gridOffset);

            // Handle case where area is outside grid
            if (minCell.x == -1 && maxCell.x == -1) return result;

            // Clamp to valid grid range
            var startX = Mathf.Max(0, Mathf.Min(minCell.x, maxCell.x));
            var endX = Mathf.Min(gridSize.x - 1, Mathf.Max(minCell.x, maxCell.x));
            var startY = Mathf.Max(0, Mathf.Min(minCell.y, maxCell.y));
            var endY = Mathf.Min(gridSize.y - 1, Mathf.Max(minCell.y, maxCell.y));

            // Adjust for one-based indexing
            if (UseOneBasedIndexing)
            {
                startX = Mathf.Max(1, startX);
                endX = Mathf.Min(gridSize.x, endX);
                startY = Mathf.Max(1, startY);
                endY = Mathf.Min(gridSize.y, endY);
            }

            // Collect all cells in the range
            for (int x = startX; x <= endX; x++)
            {
                for (int y = startY; y <= endY; y++)
                {
                    var cellCoords = new Vector2Int(x, y);
                    var cellBounds = GetCellBounds(gridBounds, gridSize, cellCoords, plane, gridOffset);

                    // Check if cell actually intersects with the world area
                    if (cellBounds.Intersects(worldArea))
                    {
                        result.Add(cellCoords);
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Gets world positions for multiple cells at once.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">Array of cell coordinates to get positions for.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <returns>Array of world positions corresponding to the input cell coordinates.</returns>
        public static Vector3[] GetWorldPositions(Bounds gridBounds, Vector2Int gridSize, Vector2Int[] cellCoords, GridPlane plane)
        {
            return GetWorldPositions(gridBounds, gridSize, cellCoords, plane, new Vector2(0.5f, 0.5f));
        }

        /// <summary>
        /// Gets world positions for multiple cells at once with custom cell offset.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">Array of cell coordinates to get positions for.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="cellOffset">The offset within each cell (0,0 = bottom-left corner, 0.5,0.5 = center, 1,1 = top-right corner).</param>
        /// <returns>Array of world positions corresponding to the input cell coordinates.</returns>
        public static Vector3[] GetWorldPositions(Bounds gridBounds, Vector2Int gridSize, Vector2Int[] cellCoords, GridPlane plane, Vector2 cellOffset)
        {
            return GetWorldPositions(gridBounds, gridSize, cellCoords, plane, cellOffset, Vector3.zero);
        }

        /// <summary>
        /// Gets world positions for multiple cells at once with custom cell offset and grid positioning offset.
        /// </summary>
        /// <param name="gridBounds">The world space bounds defining the total grid area.</param>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">Array of cell coordinates to get positions for.</param>
        /// <param name="plane">The plane (XY, XZ, YZ) to project the grid onto.</param>
        /// <param name="cellOffset">The offset within each cell (0,0 = bottom-left corner, 0.5,0.5 = center, 1,1 = top-right corner).</param>
        /// <param name="gridOffset">World space offset that was applied to the grid position.</param>
        /// <returns>Array of world positions corresponding to the input cell coordinates.</returns>
        public static Vector3[] GetWorldPositions(Bounds gridBounds, Vector2Int gridSize, Vector2Int[] cellCoords, GridPlane plane, Vector2 cellOffset, Vector3 gridOffset)
        {
            if (cellCoords == null)
            {
                Debug.LogError("Cell coordinates array cannot be null.");
                return new Vector3[0];
            }

            var result = new Vector3[cellCoords.Length];
            for (int i = 0; i < cellCoords.Length; i++)
            {
                result[i] = GetWorldPosition(gridBounds, gridSize, cellCoords[i], plane, cellOffset, gridOffset);
            }

            return result;
        }

        /// <summary>
        /// Checks if cell coordinates are valid for the given grid size.
        /// </summary>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The cell coordinates to validate.</param>
        /// <returns>True if the cell coordinates are within the grid bounds, false otherwise.</returns>
        public static bool IsValidCell(Vector2Int gridSize, Vector2Int cellCoords)
        {
            if (gridSize.x <= 0 || gridSize.y <= 0) return false;

            if (UseOneBasedIndexing)
            {
                return cellCoords.x >= 1 && cellCoords.x <= gridSize.x &&
                       cellCoords.y >= 1 && cellCoords.y <= gridSize.y;
            }
            else
            {
                return cellCoords.x >= 0 && cellCoords.x < gridSize.x &&
                       cellCoords.y >= 0 && cellCoords.y < gridSize.y;
            }
        }

        /// <summary>
        /// Clamps cell coordinates to valid grid bounds.
        /// </summary>
        /// <param name="gridSize">The dimensions of the grid (columns X, rows Y).</param>
        /// <param name="cellCoords">The cell coordinates to clamp.</param>
        /// <returns>Cell coordinates clamped to the valid grid range.</returns>
        public static Vector2Int ClampToGrid(Vector2Int gridSize, Vector2Int cellCoords)
        {
            if (gridSize.x <= 0 || gridSize.y <= 0)
            {
                Debug.LogError("Grid size must have positive dimensions.");
                return UseOneBasedIndexing ? Vector2Int.one : Vector2Int.zero;
            }

            if (UseOneBasedIndexing)
            {
                return new Vector2Int(
                    Mathf.Clamp(cellCoords.x, 1, gridSize.x),
                    Mathf.Clamp(cellCoords.y, 1, gridSize.y)
                );
            }
            else
            {
                return new Vector2Int(
                    Mathf.Clamp(cellCoords.x, 0, gridSize.x - 1),
                    Mathf.Clamp(cellCoords.y, 0, gridSize.y - 1)
                );
            }
        }

        /// <summary>
        /// Gets the Manhattan distance between two cells.
        /// </summary>
        /// <param name="cellA">First cell coordinates.</param>
        /// <param name="cellB">Second cell coordinates.</param>
        /// <returns>The Manhattan distance (sum of absolute differences in X and Y).</returns>
        public static int GetManhattanDistance(Vector2Int cellA, Vector2Int cellB)
        {
            return Mathf.Abs(cellA.x - cellB.x) + Mathf.Abs(cellA.y - cellB.y);
        }

        /// <summary>
        /// Gets all cells within a certain Manhattan distance from a center cell.
        /// </summary>
        /// <param name="centerCell">The center cell coordinates.</param>
        /// <param name="radius">The maximum Manhattan distance (inclusive).</param>
        /// <param name="gridSize">The dimensions of the grid to clamp results to.</param>
        /// <returns>List of cell coordinates within the specified radius.</returns>
        public static System.Collections.Generic.List<Vector2Int> GetCellsInRadius(Vector2Int centerCell, int radius, Vector2Int gridSize)
        {
            var result = new System.Collections.Generic.List<Vector2Int>();

            if (radius < 0)
            {
                Debug.LogError("Radius must be non-negative.");
                return result;
            }

            if (gridSize.x <= 0 || gridSize.y <= 0)
            {
                Debug.LogError("Grid size must have positive dimensions.");
                return result;
            }

            for (int x = centerCell.x - radius; x <= centerCell.x + radius; x++)
            {
                for (int y = centerCell.y - radius; y <= centerCell.y + radius; y++)
                {
                    var cellCoords = new Vector2Int(x, y);

                    // Check if within Manhattan distance
                    if (GetManhattanDistance(centerCell, cellCoords) <= radius)
                    {
                        // Check if within grid bounds
                        if (IsValidCell(gridSize, cellCoords))
                        {
                            result.Add(cellCoords);
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Gets all cells in a straight line between two cells (Bresenham's line algorithm).
        /// </summary>
        /// <param name="startCell">Starting cell coordinates.</param>
        /// <param name="endCell">Ending cell coordinates.</param>
        /// <param name="gridSize">The dimensions of the grid to validate against.</param>
        /// <returns>List of cell coordinates forming a line from start to end.</returns>
        public static System.Collections.Generic.List<Vector2Int> GetCellsInLine(Vector2Int startCell, Vector2Int endCell, Vector2Int gridSize)
        {
            var result = new System.Collections.Generic.List<Vector2Int>();

            if (gridSize.x <= 0 || gridSize.y <= 0)
            {
                Debug.LogError("Grid size must have positive dimensions.");
                return result;
            }

            // Bresenham's line algorithm
            int x0 = startCell.x, y0 = startCell.y;
            int x1 = endCell.x, y1 = endCell.y;

            int dx = Mathf.Abs(x1 - x0);
            int dy = Mathf.Abs(y1 - y0);
            int sx = x0 < x1 ? 1 : -1;
            int sy = y0 < y1 ? 1 : -1;
            int err = dx - dy;

            int x = x0, y = y0;

            while (true)
            {
                var cellCoords = new Vector2Int(x, y);
                if (IsValidCell(gridSize, cellCoords))
                {
                    result.Add(cellCoords);
                }

                if (x == x1 && y == y1) break;

                int e2 = 2 * err;
                if (e2 > -dy)
                {
                    err -= dy;
                    x += sx;
                }
                if (e2 < dx)
                {
                    err += dx;
                    y += sy;
                }
            }

            return result;
        }

        /// <summary>
        /// Gets the primary (u), secondary (v), and normal (w) axes for a given plane.
        /// </summary>
        /// <param name="plane">The grid plane.</param>
        /// <param name="u">Primary axis (grid X).</param>
        /// <param name="v">Secondary axis (grid Y).</param>
        /// <param name="w">Normal axis (depth).</param>
        public static void GetPlaneVectors(GridPlane plane, out Vector3 u, out Vector3 v, out Vector3 w)
        {
            switch (plane)
            {
                case GridPlane.XZ:
                    u = Vector3.right;
                    v = Vector3.forward;
                    w = Vector3.up;
                    break;
                case GridPlane.YZ:
                    u = Vector3.up;
                    v = Vector3.forward;
                    w = Vector3.right;
                    break;
                case GridPlane.XY:
                default:
                    u = Vector3.right;
                    v = Vector3.up;
                    w = Vector3.forward;
                    break;
            }
        }

        /// <summary>
        /// Tries to get the bounding box of a GameObject by looking for a Renderer or Collider.
        /// </summary>
        /// <param name="go">The GameObject to check.</param>
        /// <param name="bounds">The resulting bounds if found.</param>
        /// <returns>True if bounds were found, otherwise false.</returns>
        public static bool TryGetBounds(GameObject go, out Bounds bounds)
        {
            if (go == null)
            {
                bounds = default;
                return false;
            }
            if (go.TryGetComponent<Renderer>(out var renderer))
            {
                bounds = renderer.bounds;
                return true;
            }
            if (go.TryGetComponent<Collider>(out var collider))
            {
                bounds = collider.bounds;
                return true;
            }
            if (go.TryGetComponent<Collider2D>(out var collider2D))
            {
                bounds = collider2D.bounds;
                return true;
            }
            Debug.LogError($"Could not find a Renderer, Collider, or Collider2D on GameObject '{go.name}' to determine its bounds.", go);
            bounds = default;
            return false;
        }
    }
}